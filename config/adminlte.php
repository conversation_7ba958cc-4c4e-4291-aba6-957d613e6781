<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Title
    |--------------------------------------------------------------------------
    |
    | Here you can change the default title of your admin panel.
    |
    | For detailed instructions you can look the title section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'title' => 'Hodor',
    'title_prefix' => '',
    'title_postfix' => '',

    /*
    |--------------------------------------------------------------------------
    | Favicon
    |--------------------------------------------------------------------------
    |
    | Here you can activate the favicon.
    |
    | For detailed instructions you can look the favicon section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'use_ico_only' => false,
    'use_full_favicon' => false,

    /*
    |--------------------------------------------------------------------------
    | Google Fonts
    |--------------------------------------------------------------------------
    |
    | Here you can allow or not the use of external google fonts. Disabling the
    | google fonts may be useful if your admin panel internet access is
    | restricted somehow.
    |
    | For detailed instructions you can look the google fonts section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'google_fonts' => [
        'allowed' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Admin Panel Logo
    |--------------------------------------------------------------------------
    |
    | Here you can change the logo of your admin panel.
    |
    | For detailed instructions you can look the logo section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'logo' => '&nbsp;',
    'logo_img' => 'images/logo.svg',
    'logo_img_class' => 'brand-image elevation-3',
    'logo_img_xl' => null,
    'logo_img_xl_class' => 'brand-image-xs',
    'logo_img_alt' => 'Admin',

    /*
    |--------------------------------------------------------------------------
    | Authentication Logo
    |--------------------------------------------------------------------------
    |
    | Here you can setup an alternative logo to use on your login and register
    | screens. When disabled, the admin panel logo will be used instead.
    |
    | For detailed instructions you can look the auth logo section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'auth_logo' => [
        'enabled' => false,
        'img' => [
            'path' => 'vendor/adminlte/dist/img/AdminLTELogo.png',
            'alt' => 'Auth Logo',
            'class' => '',
            'width' => 50,
            'height' => 50,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Preloader Animation
    |--------------------------------------------------------------------------
    |
    | Here you can change the preloader animation configuration.
    |
    | For detailed instructions you can look the preloader section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'preloader' => [
        'enabled' => false,
        'img' => [
            'path' => 'vendor/adminlte/dist/img/AdminLTELogo.png',
            'alt' => 'AdminLTE Preloader Image',
            'effect' => 'animation__shake',
            'width' => 60,
            'height' => 60,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | User Menu
    |--------------------------------------------------------------------------
    |
    | Here you can activate and change the user menu.
    |
    | For detailed instructions you can look the user menu section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'usermenu_enabled' => false,
    'usermenu_header' => false,
    'usermenu_header_class' => 'bg-primary',
    'usermenu_image' => false,
    'usermenu_desc' => false,
    'usermenu_profile_url' => false,

    /*
    |--------------------------------------------------------------------------
    | Layout
    |--------------------------------------------------------------------------
    |
    | Here we change the layout of your admin panel.
    |
    | For detailed instructions you can look the layout section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Layout-and-Styling-Configuration
    |
    */

    'layout_topnav' => null,
    'layout_boxed' => null,
    'layout_fixed_sidebar' => null,
    'layout_fixed_navbar' => null,
    'layout_fixed_footer' => null,
    'layout_dark_mode' => false,

    /*
    |--------------------------------------------------------------------------
    | Authentication Views Classes
    |--------------------------------------------------------------------------
    |
    | Here you can change the look and behavior of the authentication views.
    |
    | For detailed instructions you can look the auth classes section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Layout-and-Styling-Configuration
    |
    */

    'classes_auth_card' => 'card-outline card-primary',
    'classes_auth_header' => '',
    'classes_auth_body' => '',
    'classes_auth_footer' => '',
    'classes_auth_icon' => '',
    'classes_auth_btn' => 'btn-flat btn-primary',

    /*
    |--------------------------------------------------------------------------
    | Admin Panel Classes
    |--------------------------------------------------------------------------
    |
    | Here you can change the look and behavior of the admin panel.
    |
    | For detailed instructions you can look the admin panel classes here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Layout-and-Styling-Configuration
    |
    */

    'classes_body' => '',
    'classes_brand' => '',
    'classes_brand_text' => '',
    'classes_content_wrapper' => '',
    'classes_content_header' => '',
    'classes_content' => '',
    'classes_sidebar' => 'sidebar-dark-primary elevation-4',
    'classes_sidebar_nav' => '',
    'classes_topnav' => 'navbar-dark',
    'classes_topnav_nav' => 'navbar-expand',
    'classes_topnav_container' => 'container',

    /*
    |--------------------------------------------------------------------------
    | Sidebar
    |--------------------------------------------------------------------------
    |
    | Here we can modify the sidebar of the admin panel.
    |
    | For detailed instructions you can look the sidebar section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Layout-and-Styling-Configuration
    |
    */

    'sidebar_mini' => 'lg',
    'sidebar_collapse' => false,
    'sidebar_collapse_auto_size' => false,
    'sidebar_collapse_remember' => false,
    'sidebar_collapse_remember_no_transition' => true,
    'sidebar_scrollbar_theme' => 'os-theme-light',
    'sidebar_scrollbar_auto_hide' => 'l',
    'sidebar_nav_accordion' => true,
    'sidebar_nav_animation_speed' => 300,

    /*
    |--------------------------------------------------------------------------
    | Control Sidebar (Right Sidebar)
    |--------------------------------------------------------------------------
    |
    | Here we can modify the right sidebar aka control sidebar of the admin panel.
    |
    | For detailed instructions you can look the right sidebar section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Layout-and-Styling-Configuration
    |
    */

    'right_sidebar' => false,
    'right_sidebar_icon' => 'fas fa-cogs',
    'right_sidebar_theme' => 'dark',
    'right_sidebar_slide' => true,
    'right_sidebar_push' => true,
    'right_sidebar_scrollbar_theme' => 'os-theme-light',
    'right_sidebar_scrollbar_auto_hide' => 'l',

    /*
    |--------------------------------------------------------------------------
    | URLs
    |--------------------------------------------------------------------------
    |
    | Here we can modify the url settings of the admin panel.
    |
    | For detailed instructions you can look the urls section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'use_route_url' => true,
    'dashboard_url' => 'home',
    'logout_url' => 'logout',
    'login_url' => 'login',
    'register_url' => 'register',
    'password_reset_url' => 'password/reset',
    'password_email_url' => 'password/email',
    'profile_url' => false,

    /*
    |--------------------------------------------------------------------------
    | Laravel Mix
    |--------------------------------------------------------------------------
    |
    | Here we can enable the Laravel Mix option for the admin panel.
    |
    | For detailed instructions you can look the laravel mix section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Other-Configuration
    |
    */

    'enabled_laravel_mix' => false,
    'laravel_mix_css_path' => 'css/app.css',
    'laravel_mix_js_path' => 'js/app.js',

    /*
    |--------------------------------------------------------------------------
    | Menu Items
    |--------------------------------------------------------------------------
    |
    | Here we can modify the sidebar/top navigation of the admin panel.
    |
    | For detailed instructions you can look here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Menu-Configuration
    |
    */

    'menu' => [
//        [
//            'text'              => 'Dashboard',
//            'route'             => 'hodor.dashboard',
//            'icon'              => 'fas fa-fw fa-home',
//        ],
        [
            'text'              => 'Cars',
            'route'             => 'hodor.listings.index',
            'icon'              => 'fas fa-fw fa-car',
            'active'            => ['hodor/listings/', 'hodor/listings/*'],
        ],
        [
            'text'              => 'Reservations',
            'icon'              => 'fas fa-fw fa-handshake',
            'submenu'           => [
                [
                    'text'          => 'Reservation List',
                    'route'         => 'hodor.reservations.index',
                    'icon'          => 'fas fa-fw fa-handshake',
                    'classes'       => 'ml-3',
                    'active'        => ['hodor/reservations/', 'hodor/reservations/*/edit'],
                ],
                [
                    'text'          => 'Duplicates',
                    'route'         => 'hodor.reservations.indexDuplicates',
                    'icon'          => 'fas fa-fw fa-handshake',
                    'classes'       => 'ml-3',
                    'active'        => ['hodor/reservations/duplicates'],
                ],
                [
                    'text'          => 'Commercial Offers',
                    'route'         => 'hodor.offers.index',
                    'icon'          => 'fas fa-fw fa-handshake',
                    'classes'       => 'ml-3',
                    'active'        => ['hodor/offers/', 'hodor/offers/*/edit'],
                ],
                [
                    'text'          => 'Price History',
                    'route'         => 'hodor.price-history.index',
                    'icon'          => 'fas fa-exchange-alt fa-rotate-90',
                    'classes'       => 'ml-3',
                    'active'        => ['hodor/price/history/', 'hodor/price/history/*'],
                ],
            ],
        ],
        [
            'text'              => 'Business Intelligence',
            'icon'              => 'fas fa-fw fa-chart-line',
            'submenu'           => [
                [
                    'text'          => 'Analytics',
                    'route'         => 'hodor.analytics.index',
                    'icon'          => 'fas fa-fw fa-chart-line mr-1',
                    'classes'       => 'ml-3',
                    'active'        => ['hodor/analytics/', 'hodor/analytics/*'],
                ],
                [
                    'text'          => 'Budget Analytics',
                    'route'         => 'hodor.analytics.budget',
                    'icon'          => 'fas fa-fw fa-chart-pie mr-1',
                    'classes'       => 'ml-3',
                    'active'        => ['hodor/budget_analytics/', 'hodor/budget_analytics/*'],
                ],
                [
                    'text'          => 'Budget Comparisons',
                    'route'         => 'hodor.analytics.budget.comparisons',
                    'icon'          => 'fas fa-fw fa-chart-pie mr-1',
                    'classes'       => 'ml-3',
                    'active'        => ['hodor/budget-comparisons/', 'hodor/budget-comparisons/*'],
                ],
                [
                    'text'          => 'Budget Per Day',
                    'route'         => 'hodor.analytics.budget.perDay',
                    'icon'          => 'fas fa-fw fa-chart-bar mr-1',
                    'classes'       => 'ml-3',
                    'active'        => ['hodor/budget-per-day/', 'hodor/budget-per-day/*'],
                ],
//                [
//                    'text'          => 'Customer Retention',
//                    'route'         => 'hodor.analytics.customer.retention',
//                    'icon'          => 'fas fa-fw fa-users mr-1',
//                    'classes'       => 'ml-3',
//                    'active'        => ['hodor/customer-retention/', 'hodor/customer-retention/*'],
//                ],
//                [
//                    'text'          => 'Fleet Utilization',
//                    'route'         => 'hodor.analytics.fleet.utilization',
//                    'icon'          => 'fas fa-fw fa-car-side mr-1',
//                    'classes'       => 'ml-3',
//                    'active'        => ['hodor/fleet-utilization/', 'hodor/fleet-utilization/*'],
//                ],
//                [
//                    'text'          => 'Seasonal Performance',
//                    'route'         => 'hodor.analytics.seasonal',
//                    'icon'          => 'fas fa-fw fa-calendar-alt mr-1',
//                    'classes'       => 'ml-3',
//                    'active'        => ['hodor/seasonal-performance/', 'hodor/seasonal-performance/*'],
//                ],
            ],
        ],
        [
            'text'              => 'Google reviews',
            'route'             => 'hodor.google-reviews.index',
            'icon'              => 'fab fa-fw fa-google',
            'active'            => ['hodor/google-reviews/', 'hodor/google-reviews/*'],
        ],
        [
            'text'              => 'Contact Leads',
            'url'               => 'hodor/contact-leads?intercepted=no',
            'icon'              => 'fas fa-fw fa-mail-bulk',
            'active'            => ['hodor/contact-leads/', 'hodor/contact-leads/*'],
        ],
        [
            'text'              => 'Blog',
            'icon'              => 'fas fa-fw fa-blog',
            'submenu'           => [
                [
                    'text'          => 'Posts',
                    'route'         => 'hodor.posts.index',
                    'icon'          => 'fas fa-fw fa-blog',
                    'classes'       => 'ml-3',
                    'active'        => ['hodor/posts/', 'hodor/posts/*'],
                ],
                [
                    'text'          => 'Tags',
                    'route'         => 'hodor.tags.index',
                    'icon'          => 'fas fa-fw fa-tags',
                    'classes'       => 'ml-3',
                    'active'        => ['hodor/tags/', 'hodor/tags/*'],
                ],
                [
                    'text'          => 'Motifs',
                    'route'         => 'hodor.motifs.index',
                    'icon'          => 'fas fa-fw fa-cube',
                    'classes'       => 'ml-3',
                    'active'        => ['hodor/motifs/', 'hodor/motifs/*'],
                ],
                [
                    'text'          => 'Photos',
                    'route'         => 'hodor.photos.index',
                    'icon'          => 'fas fa-fw fa-image',
                    'classes'       => 'ml-3',
                    'active'        => ['hodor/photos/', 'hodor/photos/*'],
                ],
                [
                    'text'          => 'Search Queries',
                    'route'         => 'hodor.search_queries.index',
                    'icon'          => 'fas fa-fw fa-search',
                    'classes'       => 'ml-3',
                    'active'        => ['hodor/search_queries/', 'hodor/search_queries/*'],
                ],
            ],
        ],
//        [
//            'text'              => 'Customers',
//            'route'             => 'hodor.customers.index',
//            'icon'              => 'fas fa-fw fa-user-friends',
//        ],
        [
            'header'            => 'Old Navigation',
            'classes'           => 'mt-5',
        ],
        [
            'text'              => 'Dashboard',
            'route'             => 'admin.dashboard',
            'icon'              => 'fas fa-fw fa-home',
        ],
        [
            'text'              => 'Content',
            'icon'              => 'fas fa-fw fa-stream',
            'submenu'           => [
                [
                    'text'          => 'Groups',
                    'route'         => 'admin.groups.index',
                    'icon'          => 'fas fa-fw fa-layer-group mr-1',
                    'classes'         => 'ml-3',
                ],
                [
                    'text'          => 'Pricing',
                    'route'         => 'admin.pricing.index',
                    'icon'          => 'fas fa-fw fa-money-bill mr-1',
                    'classes'       => 'ml-3',
                ],
                [
                    'text'          => 'Accessories',
                    'route'         => 'admin.accessories.index',
                    'icon'          => 'fas fa-fw fa-puzzle-piece mr-1',
                    'classes'       => 'ml-3',
                ],
                [
                    'text'          => 'Locations',
                    'route'         => 'admin.locations.index',
                    'icon'          => 'fas fa-fw fa-map-marked-alt mr-1',
                    'classes'       => 'ml-3',
                ],
                [
                    'text'          => 'Coupons',
                    'route'         => 'admin.coupons.index',
                    'icon'          => 'fas fa-fw fa-ticket-alt mr-1',
                    'classes'       => 'ml-3',
                ],
                [
                    'text'          => 'Popup',
                    'route'         => 'admin.popup.index',
                    'icon'          => 'fas fa-fw fa-expand-alt mr-1',
                    'classes'       => 'ml-3',
                ],
                [
                    'text'          => 'Categories',
                    'route'         => 'admin.categories.index',
                    'icon'          => 'fas fa-fw fa-sitemap mr-1',
                    'classes'       => 'ml-3',
                ],
            ],
        ],
        [
            'text'              => 'Reservations',
            'route'             => 'admin.reservations.index',
            'icon'              => 'fas fa-fw fa-calendar-check',
            'submenu' => [
                [
                    'text'          => 'List',
                    'route'         => 'admin.reservations.index',
                    'icon'          => 'fas fa-fw fa-list mr-1',
                    'classes'       => 'ml-3',
                ],
                [
                    'text'          => 'Customers',
                    'route'         => 'admin.customers.index',
                    'icon'          => 'fas fa-fw fa-user-friends mr-1',
                    'classes'       => 'ml-3',
                ],
                [
                    'text'          => 'Feedback',
                    'route'         => 'admin.feedback.index',
                    'icon'          => 'fas fa-fw fa-comment-alt mr-1',
                    'classes'       => 'ml-3',
                ],
                [
                    'text'          => 'Repeating clients',
                    'route'         => 'admin.repeatingClients.index',
                    'icon'          => 'fas fa-fw fa-history mr-1',
                    'classes'       => 'ml-3',
                ],
                [
                    'text'          => 'Unavailability quotes',
                    'route'         => 'admin.quotes.index',
                    'icon'          => 'fas fa-fw fa-minus-circle mr-1',
                    'classes'       => 'ml-3',
                ],
            ],
        ],
        [
            'text'              => 'Newsletters',
            'route'             => 'admin.newsletters.index',
            'icon'              => 'fas fa-fw fa-mail-bulk',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Menu Filters
    |--------------------------------------------------------------------------
    |
    | Here we can modify the menu filters of the admin panel.
    |
    | For detailed instructions you can look the menu filters section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Menu-Configuration
    |
    */

    'filters' => [
        JeroenNoten\LaravelAdminLte\Menu\Filters\GateFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\HrefFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\SearchFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\ActiveFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\ClassesFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\LangFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\DataFilter::class,
    ],

    /*
    |--------------------------------------------------------------------------
    | Plugins Initialization
    |--------------------------------------------------------------------------
    |
    | Here we can modify the plugins used inside the admin panel.
    |
    | For detailed instructions you can look the plugins section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Plugins-Configuration
    |
    */

    'plugins' => [
        'Datatables' => [
            'active' => true,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdn.datatables.net/1.10.19/js/jquery.dataTables.min.js',
                ],
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdn.datatables.net/1.10.19/js/dataTables.bootstrap4.min.js',
                ],
                [
                    'type' => 'css',
                    'asset' => false,
                    'location' => '//cdn.datatables.net/1.10.19/css/dataTables.bootstrap4.min.css',
                ],
            ],
        ],
        'Select2' => [
            'active' => true,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => true,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/js/select2.min.js',
                ],
                [
                    'type' => 'css',
                    'asset' => true,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/css/select2.css',
                ],
            ],
        ],
        'Chartjs' => [
            'active' => true,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/Chart.js/2.7.0/Chart.bundle.min.js',
                ],
            ],
        ],
        'Sweetalert2' => [
            'active' => false,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdn.jsdelivr.net/npm/sweetalert2@8',
                ],
            ],
        ],
        'Pace' => [
            'active' => false,
            'files' => [
                [
                    'type' => 'css',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/pace/1.0.2/themes/blue/pace-theme-center-radar.min.css',
                ],
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/pace/1.0.2/pace.min.js',
                ],
            ],
        ],
        'BsCustomFileInput' => [
            'active' => false,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => true,
                    'location' => 'vendor/bs-custom-file-input/bs-custom-file-input.min.js',
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | IFrame
    |--------------------------------------------------------------------------
    |
    | Here we change the IFrame mode configuration. Note these changes will
    | only apply to the view that extends and enable the IFrame mode.
    |
    | For detailed instructions you can look the iframe mode section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/IFrame-Mode-Configuration
    |
    */

    'iframe' => [
        'default_tab' => [
            'url' => null,
            'title' => null,
        ],
        'buttons' => [
            'close' => true,
            'close_all' => true,
            'close_all_other' => true,
            'scroll_left' => true,
            'scroll_right' => true,
            'fullscreen' => true,
        ],
        'options' => [
            'loading_screen' => 1000,
            'auto_show_new_tab' => true,
            'use_navbar_items' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Livewire
    |--------------------------------------------------------------------------
    |
    | Here we can enable the Livewire support.
    |
    | For detailed instructions you can look the livewire here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Other-Configuration
    |
    */

    'livewire' => false,
];
