<?php

namespace Modules\Hodor\Http\Requests;

use App\Http\Requests\Request;
use Illuminate\Support\Facades\Config;

class GroupUpdateRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'name'              => 'required',
            'engine'            => 'required',
            'transmission'      => 'required',
            'capacity'          => 'required',
            'seats'             => 'required',
            'doors'             => 'required',
            'offer_percentage'  => 'nullable|numeric|min:1|max:100',
            'fuel_plan'         => 'nullable|numeric',
            'base_price'        => 'nullable|numeric',
            'base_price_cretan' => 'nullable|numeric',
            'fuel'              => 'nullable|in:petrol,diesel',
            'on_offer'          => 'nullable|boolean',
            'or_similar'        => 'nullable|boolean',
            'supergroup_id'     => 'nullable|exists:supergroups,id',
            'related_group'     => 'nullable|array',
            'related_group.*'   => 'exists:groups,id',
        ];

        return $rules;
    }
}
