<?php

namespace Modules\Hodor\Http\Controllers;

use App\Language;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Config;

class HodorController extends Controller
{
    protected $view_data = array(
        'page_title' => 'Admin app',
    );

    public function __construct()
    {
        // Set a config item to access available translation locales through validation and everything else needed
        Config::set('translationLocales', Language::all()->pluck('locale', 'id'));
    }
}
