@extends('hodor::layouts.master')

@section('content')
<section class="content-header">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-6"></div>
            <div class="col-sm-6">
                <div class="float-right">
                    <a href="{{ route('hodor.groups.create') }}" class="btn btn-primary"><i class="fas fa-plus"></i> Group</a>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="content">
    <div class="container-fluid">
        <div class="row">
            @include('hodor::common.alert')
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div>{{ $groups->count() }} items</div>
                        <div class="card-tools">
                            <small class="text-muted">Drag and drop to reorder groups</small>
                        </div>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Sort</th>
                                    <th>ID</th>
                                    <th>Lang</th>
                                    <th>Name</th>
                                    <th>Features</th>
                                    <th>Pricing</th>
                                    <th>Related Groups</th>
                                    <th>Created</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody class="groupSort">
                            {!! Form::open(['route' => 'hodor.groups.sortOrder', 'class' => 'main']) !!}
                            @foreach ($groups as $group)
                                <tr class="groupSortItem">
                                    <td class="groupSortItemHandle" style="cursor: move;">
                                        <input type="hidden" name="groupID[]" value="{{ $group->id }}" />
                                        <i class="fas fa-grip-vertical text-muted"></i>
                                    </td>
                                    <td>
                                        <strong><a href="{{ route('hodor.groups.edit', $group->id) }}">{{ $group->id }}</a></strong>
                                    </td>
                                    <td>
                                        @foreach(array_keys($group->getTranslationsArray()) as $locale)
                                            <img src="{!! asset('images/' . $locale . '.png') !!}" />
                                        @endforeach
                                    </td>
                                    <td>
                                        <strong><a href="{{ route('hodor.groups.edit', $group->id) }}">{{ $group->full_name }}</a></strong>
                                        <div class="text-muted"><small>{{ $group->name }}</small></div>
                                    </td>
                                    <td>
                                        <small class="d-block">{{ $group->engine }}</small>
                                        <small class="d-block">{{ $group->transmission }} | {{ $group->seats }} seats | {{ $group->doors }} doors</small>
                                        @if($group->fuel)
                                            <small class="d-block text-muted">{{ ucfirst($group->fuel) }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        @if($group->fuel_plan !== null)
                                            <span class="badge bg-info">Fuel: €{{ $group->fuel_plan }}</span><br>
                                        @endif
                                        @if($group->on_offer === 1)
                                            <span class="badge bg-success">On offer: {{ $group->offerTitle }}</span>
                                        @else
                                            <span class="badge bg-secondary">Regular prices</span>
                                        @endif
                                        @if($group->base_price)
                                            <div class="text-muted"><small>Base: €{{ $group->base_price }}</small></div>
                                        @endif
                                    </td>
                                    <td>
                                        @if (count($group->relatedGroups))
                                            @foreach ($group->relatedGroups as $related)
                                                <small class="d-block">{{ $related->name }}</small>
                                            @endforeach
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>{!! $group->created_at->format('d-m-Y H:i') !!}</td>
                                    <td class="text-right project-actions">
                                        <p>
                                            <a class="btn btn-outline-info" title="Edit" href="{{ route('hodor.groups.edit', $group->id) }}">
                                                <i class="fas fa-pen"></i>
                                            </a>
                                        </p>
                                    </td>
                                </tr>
                            @endforeach
                            {!! Form::close() !!}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('js')
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
<script>
$(document).ready(function() {
    $(".groupSort").sortable({
        items: ".groupSortItem",
        update: updateGroups,
        handle: ".groupSortItemHandle",
        helper: function(e, ui) {
            ui.children().each(function() {
                $(this).width($(this).width());
            });
            return ui;
        }
    });
});

function updateGroups(data) {
    var form = $("form.main");
    var method = form.find('input[name="_method"]').val() || 'POST';

    $.ajax({
        type: method,
        url: form.prop('action'),
        headers: {
            "x-csrf-token": $('input[name="_token"]').val()
        },
        data: form.serialize(),
        dataType: 'json'
    })
    .fail(function() {
        toastr.error("Error saving group sorting.");
    })
    .done(function(data) {
        if (data.status == true) {
            toastr.success(data.message);
        } else {
            toastr.error(data.message);
        }
    });
}
</script>
@stop

@section('css')
<link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/ui-lightness/jquery-ui.css">
@stop
