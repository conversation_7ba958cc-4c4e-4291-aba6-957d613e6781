@extends('hodor::layouts.master')

@section('content')
<section class="content-header">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-6"></div>
            <div class="col-sm-6">
                <div class="float-right">
                    <a href="{{ route('hodor.groups.create') }}" class="btn btn-primary"><i class="fas fa-plus"></i> Group</a>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="content">
    <div class="container-fluid">
        <div class="row">
            @include('hodor::common.alert')
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Groups ({{ $groups->count() }} items)</h3>
                        <div class="card-tools">
                            <small class="text-muted">Drag and drop to reorder groups</small>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="groupSort">
                            {!! Form::open(['route' => 'hodor.groups.sortOrder', 'class' => 'main']) !!}
                            @foreach ($groups as $group)
                                <div class="groupSortItem border-bottom">
                                    <input type="hidden" name="groupID[]" value="{{ $group->id }}" />
                                    <div class="d-flex align-items-center p-3">
                                        <div class="groupSortItemHandle mr-3" style="cursor: move;">
                                            <i class="fas fa-grip-vertical text-muted"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <h5 class="mb-1">{{ $group->full_name }}</h5>
                                                    <small class="text-muted">ID: {{ $group->id }}</small>
                                                </div>
                                                <div class="col-md-3">
                                                    @if($group->fuel_plan !== null)
                                                        <span class="badge badge-info">Fuel plan: €{{ $group->fuel_plan }}</span><br>
                                                    @endif
                                                    @if($group->on_offer === 1)
                                                        <span class="badge badge-success">On offer: {{ $group->offerTitle }}</span>
                                                    @else
                                                        <span class="badge badge-secondary">Regular prices</span>
                                                    @endif
                                                </div>
                                                <div class="col-md-3">
                                                    @if (count($group->relatedGroups))
                                                        <small class="text-muted d-block">Related groups:</small>
                                                        @foreach ($group->relatedGroups as $related)
                                                            <small class="text-secondary">{{ $related->full_name }}</small><br>
                                                        @endforeach
                                                    @endif
                                                </div>
                                                <div class="col-md-2 text-right">
                                                    <a href="{{ route('hodor.groups.edit', $group->id) }}" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-edit"></i> Edit
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                            {!! Form::close() !!}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('js')
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
<script>
$(document).ready(function() {
    $(".groupSort").sortable({
        items: ".groupSortItem",
        update: updateGroups,
        handle: ".groupSortItemHandle"
    });
});

function updateGroups(data) {
    var form = $("form.main");
    var method = form.find('input[name="_method"]').val() || 'POST';
    
    $.ajax({
        type: method,
        url: form.prop('action'),
        headers: {
            "x-csrf-token": $('input[name="_token"]').val()
        },
        data: form.serialize(),
        dataType: 'json'
    })
    .fail(function() {
        toastr.error("Error saving group sorting.");
    })
    .done(function(data) {
        if (data.status == true) {
            toastr.success(data.message);
        } else {
            toastr.error(data.message);
        }
    });
}
</script>
@stop

@section('css')
<link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/ui-lightness/jquery-ui.css">
@stop
