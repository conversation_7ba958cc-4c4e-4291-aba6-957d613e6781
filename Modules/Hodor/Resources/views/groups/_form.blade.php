<div class="row">
    <div class="col-lg-3">
        <div class="form-group">
            {!! Form::label('name', 'Name*') !!}
            {!! Form::text('name', null, ['class' => 'form-control' . ($errors->has('name') ? ' is-invalid' : '')]) !!}
            @if($errors->has('name'))
                <div class="invalid-feedback">{{ $errors->first('name') }}</div>
            @endif
        </div>
    </div>
    <div class="col-lg-3">
        <div class="form-group">
            {!! Form::label('fuel_plan', 'Fuel Plan (€)') !!}
            {!! Form::number('fuel_plan', null, ['class' => 'form-control' . ($errors->has('fuel_plan') ? ' is-invalid' : ''), 'step' => '0.01']) !!}
            @if($errors->has('fuel_plan'))
                <div class="invalid-feedback">{{ $errors->first('fuel_plan') }}</div>
            @endif
        </div>
    </div>
    <div class="col-lg-3">
        <div class="form-group">
            {!! Form::label('or_similar', 'Or Similar') !!}
            {!! Form::select('or_similar', ['0' => 'No', '1' => 'Yes'], null, ['class' => 'form-control custom-select']) !!}
        </div>
    </div>
    <div class="col-lg-3">
        <div class="form-group">
            {!! Form::label('supergroup_id', 'Supergroup') !!}
            {!! Form::select('supergroup_id', $supergroup_list, null, ['class' => 'form-control custom-select']) !!}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-3">
        <div class="form-group">
            {!! Form::label('on_offer', 'On Offer') !!}
            {!! Form::select('on_offer', ['0' => 'No', '1' => 'Yes'], null, ['class' => 'form-control custom-select']) !!}
        </div>
    </div>
    <div class="col-lg-3">
        <div class="form-group">
            {!! Form::label('offer_percentage', 'Offer Percentage') !!}
            {!! Form::number('offer_percentage', null, ['class' => 'form-control' . ($errors->has('offer_percentage') ? ' is-invalid' : ''), 'placeholder' => 'Integer from 1 to 100 e.g. "35"', 'min' => '1', 'max' => '100']) !!}
            @if($errors->has('offer_percentage'))
                <div class="invalid-feedback">{{ $errors->first('offer_percentage') }}</div>
            @endif
        </div>
    </div>
    <div class="col-lg-3">
        <div class="form-group">
            {!! Form::label('base_price', 'Base Price (€)') !!}
            {!! Form::number('base_price', null, ['class' => 'form-control', 'step' => '0.01']) !!}
        </div>
    </div>
    <div class="col-lg-3">
        <div class="form-group">
            {!! Form::label('base_price_cretan', 'Base Price Cretan (€)') !!}
            {!! Form::number('base_price_cretan', null, ['class' => 'form-control', 'step' => '0.01']) !!}
        </div>
    </div>
    <div class="col-lg-3">
        <div class="form-group">
            {!! Form::label('excess', 'Excess (€)') !!}
            {!! Form::number('excess', null, ['class' => 'form-control' . ($errors->has('excess') ? ' is-invalid' : ''), 'min' => '0', 'placeholder' => 'Enter excess amount']) !!}
            @if($errors->has('excess'))
                <div class="invalid-feedback">{{ $errors->first('excess') }}</div>
            @endif
        </div>
    </div>
</div>

<!-- Group Features -->
<div class="card mt-3">
    <div class="card-header">
        <h5 class="card-title">Group Features <small class="text-muted">(For CretanRentals groups list)</small></h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-lg-4">
                <div class="form-group">
                    {!! Form::label('engine', 'Engine*') !!}
                    {!! Form::text('engine', null, ['class' => 'form-control' . ($errors->has('engine') ? ' is-invalid' : ''), 'placeholder' => 'Free text e.g. "1400cc to 1600cc"']) !!}
                    @if($errors->has('engine'))
                        <div class="invalid-feedback">{{ $errors->first('engine') }}</div>
                    @endif
                </div>
            </div>
            <div class="col-lg-4">
                <div class="form-group">
                    {!! Form::label('doors', 'Doors*') !!}
                    {!! Form::text('doors', null, ['class' => 'form-control' . ($errors->has('doors') ? ' is-invalid' : ''), 'placeholder' => 'e.g. "4" or "4-5"']) !!}
                    @if($errors->has('doors'))
                        <div class="invalid-feedback">{{ $errors->first('doors') }}</div>
                    @endif
                </div>
            </div>
            <div class="col-lg-4">
                <div class="form-group">
                    {!! Form::label('seats', 'Seats*') !!}
                    {!! Form::text('seats', null, ['class' => 'form-control' . ($errors->has('seats') ? ' is-invalid' : ''), 'placeholder' => 'e.g. "4" or "4-5"']) !!}
                    @if($errors->has('seats'))
                        <div class="invalid-feedback">{{ $errors->first('seats') }}</div>
                    @endif
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-4">
                <div class="form-group">
                    {!! Form::label('capacity', 'Luggage*') !!}
                    {!! Form::text('capacity', null, ['class' => 'form-control' . ($errors->has('capacity') ? ' is-invalid' : ''), 'placeholder' => 'e.g. "3" or "3-4"']) !!}
                    @if($errors->has('capacity'))
                        <div class="invalid-feedback">{{ $errors->first('capacity') }}</div>
                    @endif
                </div>
            </div>
            <div class="col-lg-4">
                <div class="form-group">
                    {!! Form::label('transmission', 'Transmission*') !!}
                    {!! Form::select('transmission', ['automatic' => 'Automatic', 'manual' => 'Manual'], null, ['class' => 'form-control custom-select' . ($errors->has('transmission') ? ' is-invalid' : '')]) !!}
                    @if($errors->has('transmission'))
                        <div class="invalid-feedback">{{ $errors->first('transmission') }}</div>
                    @endif
                </div>
            </div>
            <div class="col-lg-4">
                <div class="form-group">
                    {!! Form::label('fuel', 'Fuel') !!}
                    {!! Form::select('fuel', ['petrol' => 'Petrol', 'diesel' => 'Diesel'], null, ['class' => 'form-control custom-select']) !!}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Related Groups -->
<div class="card mt-3">
    <div class="card-header">
        <h5 class="card-title">Related Groups</h5>
    </div>
    <div class="card-body">
        <div class="form-group">
            {!! Form::label('related_group', 'Related Groups') !!}
            {!! Form::select('related_group[]', $group_list, $related_groups_list, ['multiple' => 'multiple', 'class' => 'form-control select2']) !!}
        </div>
    </div>
</div>
