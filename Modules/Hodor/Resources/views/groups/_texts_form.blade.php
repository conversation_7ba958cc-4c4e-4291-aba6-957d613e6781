<div class="row">
    @foreach ($languages as $locale)
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ strtoupper($locale) }} Texts</h3>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="description-{{ $locale }}">
                            Description ({{ strtoupper($locale) }})*
                        </label>
                        {!! Form::text($locale . '[description]', $group->translate($locale)->description ?? '', ['id' => 'description-' . $locale . '-' . $group->id, 'class' => 'form-control' . ($errors->has($locale . '.description') ? ' is-invalid' : null)]) !!}
                        @if($errors->has($locale . '.description'))
                            <div class="invalid-feedback">{{ $errors->first($locale . '.description') }}</div>
                        @endif
                        @include('hodor::common._translationSuggestCta', ['model' => $group, 'field' => 'description', 'locale' => $locale, 'wysiwyg' => 'no'])
                    </div>

                    <div class="form-group">
                        <label for="description_cr-{{ $locale }}">
                            Description for CretanRentals ({{ strtoupper($locale) }})
                        </label>
                        {!! Form::text($locale . '[description_cr]', $group->translate($locale)->description_cr ?? '', ['id' => 'description_cr-' . $locale . '-' . $group->id, 'class' => 'form-control']) !!}
                        @include('hodor::common._translationSuggestCta', ['model' => $group, 'field' => 'description_cr', 'locale' => $locale, 'wysiwyg' => 'no'])
                    </div>

                    <div class="form-group">
                        <label for="seo_text-{{ $locale }}">
                            SEO Text ({{ strtoupper($locale) }})
                        </label>
                        {!! Form::textarea($locale . '[seo_text]', $group->translate($locale)->seo_text ?? '', ['id' => 'seo_text-' . $locale . '-' . $group->id, 'class' => 'form-control seo_text', 'rows' => 6]) !!}
                        @include('hodor::common._translationSuggestCta', ['model' => $group, 'field' => 'seo_text', 'locale' => $locale, 'wysiwyg' => 'yes'])
                    </div>

                    <div class="form-group">
                        <label for="seo_text_cr-{{ $locale }}">
                            SEO Text for CretanRentals ({{ strtoupper($locale) }})
                        </label>
                        {!! Form::textarea($locale . '[seo_text_cr]', $group->translate($locale)->seo_text_cr ?? '', ['id' => 'seo_text_cr-' . $locale . '-' . $group->id, 'class' => 'form-control seo_text', 'rows' => 6]) !!}
                        @include('hodor::common._translationSuggestCta', ['model' => $group, 'field' => 'seo_text_cr', 'locale' => $locale, 'wysiwyg' => 'yes'])
                    </div>
                </div>
            </div>
        </div>
        @if($loop->iteration % 2 == 0)
            </div><div class="row">
        @endif
    @endforeach
</div>

@section('js')
<script src="{{ asset('packages/ckeditor/ckeditor.js') }}"></script>
<script>
$(document).ready(function() {
    // Initialize CKEditor for SEO text areas
    $('.seo_text').each(function() {
        CKEDITOR.replace(this.id || this.name);
    });
});
</script>
@stop
