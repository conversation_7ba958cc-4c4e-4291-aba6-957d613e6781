@extends('hodor::layouts.master')

@section('content')
<section class="content">
    <div class="container-fluid">
        <div class="card card-default">
            <div class="card-header">
                <h3 class="card-title">Group Details</h3>
            </div>
            @include('hodor::common.alert')
            {!! Form::open(['method' => 'POST', 'class' => 'main', 'route' => ['hodor.groups.store']]) !!}
            {!! Form::token() !!}
            <div class="card-body">
                @include('hodor::groups._form')
            </div>
            <div class="card-footer">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Create Group
                </button>
                <a href="{{ route('hodor.groups.index') }}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
            </div>
            {!! Form::close() !!}
        </div>
    </div>
</section>
@endsection

@section('js')
<script src="{{ asset('packages/ckeditor/ckeditor.js') }}"></script>
<script>
$(document).ready(function() {
    $('.select2').select2();
    
    // Initialize CKEditor for SEO text areas
    $('.seo_text').each(function() {
        CKEDITOR.replace(this.id || this.name);
    });
});
</script>
@stop
