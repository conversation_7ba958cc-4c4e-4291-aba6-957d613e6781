@extends('hodor::layouts.master')

@section('content')
<section class="content">
    <div class="container-fluid">
        <div class="card card-default">
            <div class="card-header">
                <h3 class="card-title">Edit Group: {{ $group->name }}</h3>
            </div>
            @include('hodor::common.alert')
            {!! Form::model($group, ['method' => 'PUT', 'class' => 'main', 'route' => ['hodor.groups.update', $group->id]]) !!}
            {!! Form::token() !!}
            <div class="card-body">
                @include('hodor::groups._form')
            </div>
            <div class="card-footer">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Update Group
                </button>
                <a href="{{ route('hodor.groups.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Groups
                </a>
            </div>
            {!! Form::close() !!}
        </div>
    </div>
</section>
@endsection

@section('js')
<script src="{{ asset('packages/ckeditor/ckeditor.js') }}"></script>
<script>
$(document).ready(function() {
    $('.select2').select2();
    
    // Initialize CKEditor for SEO text areas
    $('.seo_text').each(function() {
        CKEDITOR.replace(this.id || this.name);
    });
});
</script>
@stop
