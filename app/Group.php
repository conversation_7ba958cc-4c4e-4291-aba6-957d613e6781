<?php namespace App;

use App\Traits\AccessorsMutators\GroupAccessorsMutators;
use App\Traits\Relationships\GroupRelationships;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Model;

class Group extends Model implements TranslatableContract{

	use Translatable,
        GroupRelationships,
        GroupAccessorsMutators;

	public $translatedAttributes = ['description', 'seo_text', 'description_cr', 'seo_text_cr'];

    protected $fillable = [
        'name',
        'description',
        'description_cr',
        'base_price',
        'base_price_cretan',
        'fuel_plan',
        'engine',
        'doors',
        'capacity',
        'transmission',
        'seats',
        'fuel',
        'seo_text',
        'seo_text_cr',
        'on_offer',
        'offer_percentage',
        'or_similar',
        'supergroup_id',
    ];

    /**
     *
     * @param array $attributes
     *
     * @return void
     */
    protected function performCustomCreationTasks(array $attributes = [])
    {
        return $this;
    }

    /**
     *
     * @param array $attributes
     *
     * @return void
     */
    protected function performCustomPostCreationTasks(array $attributes = [])
    {
        if (!empty($attributes['related_group']))
        {
            $this->relatedGroups()->attach($attributes['related_group']);
        }
        return $this;
    }

    /**
     * @param array $attributes
     *
     * @return void
     */
    protected function performCustomUpdateTasks(array $attributes = [])
    {
        if (!empty($attributes['related_group']))
        {
            $this->relatedGroups()->sync($attributes['related_group']);
        }

        return $this;
    }

}
